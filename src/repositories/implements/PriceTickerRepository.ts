import {Op} from "sequelize";
import {ListOfTickers} from "../../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../../entities/consolidated_data/StatisticsOfTickers";
import {iStatisticsOfTicker} from "../../entities/consolidated_data/iStatisticsOfTicker";
import {ApiEOD} from "../../providers/implements/ApiEOD";
import {iPriceTickerRepository} from "../iPriceTickerRepository";
import {addLogJobExecution, getPriceBatchSize, getPriceChunkSize} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";
import {RealTimePriceEODHD} from "./RealTimePriceDAO";

export class PriceTickerRepository implements iPriceTickerRepository {
    array_statistics: iStatisticsOfTicker[];
    tickers: ListOfTickers[];
    statisticsToCreate: iStatisticsOfTicker[];
    api: ApiEOD;
    current_valid_prices: RealTimePriceEODHD[];
    current_invalid_prices: RealTimePriceEODHD[];

    constructor() {
        this.api = new ApiEOD();
        this.statisticsToCreate = [];
    }

    async getPrice(updatedTickers: ListOfTickers[]): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]> {
        this.tickers = updatedTickers;
        const valid_prices: RealTimePriceEODHD[] = [];
        const invalid_prices: RealTimePriceEODHD[] = [];

        if (this.tickers.length === 0) {
            return [valid_prices, invalid_prices];
        }

        // Get configurable batch size
        const batchSize = await getPriceBatchSize();
        console.log(`Processing ${this.tickers.length} tickers with batch size: ${batchSize}`);

        addLogJobExecution(LogLevel.INFO, "getPrice", "Starting batch price processing", {
            totalTickers: this.tickers.length,
            batchSize: batchSize,
        });

        // Process tickers in batches
        const batches = this.createBatches(this.tickers, batchSize);

        // Determine if we should process batches concurrently or sequentially
        const maxConcurrentBatches = Math.min(3, batches.length); // Limit concurrent batches to avoid overwhelming the API
        const shouldProcessConcurrently = batches.length > 1 && maxConcurrentBatches > 1;

        if (shouldProcessConcurrently) {
            console.log(`Processing ${batches.length} batches with max ${maxConcurrentBatches} concurrent batches`);
            await this.processBatchesConcurrently(batches, valid_prices, invalid_prices, maxConcurrentBatches);
        } else {
            console.log(`Processing ${batches.length} batches sequentially`);
            await this.processBatchesSequentially(batches, valid_prices, invalid_prices);
        }

        this.current_valid_prices = valid_prices;
        this.current_invalid_prices = invalid_prices;

        addLogJobExecution(LogLevel.INFO, "getPrice", "Price processing completed", {
            totalTickers: this.tickers.length,
            totalValidPrices: valid_prices.length,
            totalInvalidPrices: invalid_prices.length,
            batchesProcessed: batches.length,
        });

        return [valid_prices, invalid_prices];
    }

    /**
     * Creates batches of tickers for batch processing
     */
    private createBatches(tickers: ListOfTickers[], batchSize: number): ListOfTickers[][] {
        const batches: ListOfTickers[][] = [];
        for (let i = 0; i < tickers.length; i += batchSize) {
            batches.push(tickers.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * Processes a batch of tickers using the EODHD batch API
     */
    private async processBatch(batch: ListOfTickers[], batchIndex: number, totalBatches: number): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]> {
        const valid_prices: RealTimePriceEODHD[] = [];
        const invalid_prices: RealTimePriceEODHD[] = [];

        if (batch.length === 0) {
            return [valid_prices, invalid_prices];
        }

        // Prepare the primary ticker and others array
        const primaryTicker = batch[0].primary_ticker_eodhd;
        const otherTickers = batch.length > 1 ? batch.slice(1).map((ticker) => ticker.primary_ticker_eodhd) : undefined;

        console.log(`[Batch ${batchIndex}/${totalBatches}] API call for ${batch.length} tickers: ${primaryTicker}${otherTickers ? ` + ${otherTickers.length} others` : ""}`);

        try {
            const response = await this.api.findPrice(primaryTicker, otherTickers);

            // Process the response
            if (Array.isArray(response)) {
                // Multiple tickers response
                for (let i = 0; i < response.length; i++) {
                    const priceData = response[i];
                    const ticker = batch[i]; // Match by index

                    if (priceData.timestamp !== "NA") {
                        valid_prices.push({...priceData, ticker_internal_id: ticker.id});
                    } else {
                        invalid_prices.push({...priceData, ticker_internal_id: ticker.id});
                    }
                }
            } else {
                // Single ticker response
                const ticker = batch[0];
                if (response.timestamp !== "NA") {
                    valid_prices.push({...response, ticker_internal_id: ticker.id});
                } else {
                    invalid_prices.push({...response, ticker_internal_id: ticker.id});
                }
            }
        } catch (err: any) {
            console.error(`[Batch ${batchIndex}/${totalBatches}] API error:`, err.message);
            throw err; // Re-throw to trigger fallback processing
        }

        return [valid_prices, invalid_prices];
    }

    /**
     * Fallback method to process tickers individually when batch processing fails
     */
    private async processBatchIndividually(batch: ListOfTickers[], batchIndex: number): Promise<[RealTimePriceEODHD[], RealTimePriceEODHD[]]> {
        const valid_prices: RealTimePriceEODHD[] = [];
        const invalid_prices: RealTimePriceEODHD[] = [];

        console.log(`[Batch ${batchIndex}] Falling back to individual processing for ${batch.length} tickers`);

        for (let i = 0; i < batch.length; i++) {
            const ticker = batch[i];
            console.log(`[Batch ${batchIndex}] Individual processing: ${ticker.primary_ticker_eodhd} (${i + 1}/${batch.length})`);

            try {
                const response = await this.api.findPrice(ticker.primary_ticker_eodhd);

                if (Array.isArray(response)) {
                    for (let j = 0; j < response.length; j++) {
                        if (response[j].timestamp !== "NA") {
                            valid_prices.push({...response[j], ticker_internal_id: ticker.id});
                        } else {
                            invalid_prices.push({...response[j], ticker_internal_id: ticker.id});
                        }
                    }
                } else {
                    if (response.timestamp !== "NA") {
                        valid_prices.push({...response, ticker_internal_id: ticker.id});
                    } else {
                        invalid_prices.push({...response, ticker_internal_id: ticker.id});
                    }
                }
            } catch (err: any) {
                console.error(`[Batch ${batchIndex}] Individual error for ${ticker.primary_ticker_eodhd}:`, err.message);
                addLogJobExecution(
                    LogLevel.ERROR,
                    "getPrice",
                    "Individual ticker processing error",
                    {
                        error: err instanceof Error ? err.message : String(err),
                        ticker: ticker.primary_ticker_eodhd,
                    },
                    ticker.id,
                );
                continue;
            }
        }

        return [valid_prices, invalid_prices];
    }

    /**
     * Processes batches sequentially (original behavior)
     */
    private async processBatchesSequentially(batches: ListOfTickers[][], valid_prices: RealTimePriceEODHD[], invalid_prices: RealTimePriceEODHD[]): Promise<void> {
        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
            const batch = batches[batchIndex];
            const batchStartTime = Date.now();

            console.log(`[Batch ${batchIndex + 1}/${batches.length}] Processing ${batch.length} tickers`);

            try {
                const [batchValidPrices, batchInvalidPrices] = await this.processBatch(batch, batchIndex + 1, batches.length);
                valid_prices.push(...batchValidPrices);
                invalid_prices.push(...batchInvalidPrices);

                const batchDuration = Date.now() - batchStartTime;
                console.log(`[Batch ${batchIndex + 1}/${batches.length}] Completed in ${batchDuration}ms`);

                addLogJobExecution(LogLevel.INFO, "getPrice", "Batch processed successfully", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    batchSize: batch.length,
                    validPrices: batchValidPrices.length,
                    invalidPrices: batchInvalidPrices.length,
                    durationMs: batchDuration,
                });
            } catch (err: any) {
                console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing batch:`, err.message);
                addLogJobExecution(LogLevel.ERROR, "getPrice", "Batch processing error", {
                    batchIndex: batchIndex + 1,
                    totalBatches: batches.length,
                    error: err instanceof Error ? err.message : String(err),
                });

                // Fallback to individual processing for failed batch
                const [fallbackValidPrices, fallbackInvalidPrices] = await this.processBatchIndividually(batch, batchIndex + 1);
                valid_prices.push(...fallbackValidPrices);
                invalid_prices.push(...fallbackInvalidPrices);
            }
        }
    }

    /**
     * Processes batches concurrently with limited concurrency
     */
    private async processBatchesConcurrently(batches: ListOfTickers[][], valid_prices: RealTimePriceEODHD[], invalid_prices: RealTimePriceEODHD[], maxConcurrency: number): Promise<void> {
        // Process batches in chunks to limit concurrency
        for (let i = 0; i < batches.length; i += maxConcurrency) {
            const batchChunk = batches.slice(i, i + maxConcurrency);
            const chunkPromises = batchChunk.map(async (batch, chunkIndex) => {
                const batchIndex = i + chunkIndex;
                const batchStartTime = Date.now();

                console.log(`[Batch ${batchIndex + 1}/${batches.length}] Processing ${batch.length} tickers (concurrent)`);

                try {
                    const [batchValidPrices, batchInvalidPrices] = await this.processBatch(batch, batchIndex + 1, batches.length);

                    const batchDuration = Date.now() - batchStartTime;
                    console.log(`[Batch ${batchIndex + 1}/${batches.length}] Completed in ${batchDuration}ms (concurrent)`);

                    addLogJobExecution(LogLevel.INFO, "getPrice", "Concurrent batch processed successfully", {
                        batchIndex: batchIndex + 1,
                        totalBatches: batches.length,
                        batchSize: batch.length,
                        validPrices: batchValidPrices.length,
                        invalidPrices: batchInvalidPrices.length,
                        durationMs: batchDuration,
                    });

                    return {batchValidPrices, batchInvalidPrices};
                } catch (err: any) {
                    console.error(`[Batch ${batchIndex + 1}/${batches.length}] Error processing concurrent batch:`, err.message);
                    addLogJobExecution(LogLevel.ERROR, "getPrice", "Concurrent batch processing error", {
                        batchIndex: batchIndex + 1,
                        totalBatches: batches.length,
                        error: err instanceof Error ? err.message : String(err),
                    });

                    // Fallback to individual processing for failed batch
                    const [fallbackValidPrices, fallbackInvalidPrices] = await this.processBatchIndividually(batch, batchIndex + 1);
                    return {batchValidPrices: fallbackValidPrices, batchInvalidPrices: fallbackInvalidPrices};
                }
            });

            // Wait for all batches in this chunk to complete
            const chunkResults = await Promise.all(chunkPromises);

            // Aggregate results
            for (const result of chunkResults) {
                valid_prices.push(...result.batchValidPrices);
                invalid_prices.push(...result.batchInvalidPrices);
            }
        }
    }

    /**
     * Batch method to set multiple prices efficiently with chunked processing
     */
    async setPrices(tickerPrices: RealTimePriceEODHD[]): Promise<void> {
        if (tickerPrices.length === 0) {
            return;
        }

        console.log(`Setting prices for ${tickerPrices.length} tickers`);
        const startTime = Date.now();

        // For large datasets, process in chunks to avoid timeouts
        const chunkSize = await getPriceChunkSize(); // Get configurable chunk size
        const chunks = this.createPriceChunks(tickerPrices, chunkSize);

        console.log(`Processing ${tickerPrices.length} tickers in ${chunks.length} chunks of ${chunkSize}`);

        let totalUpdates = 0;
        let totalCreates = 0;
        let processedCount = 0;

        try {
            for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
                const chunk = chunks[chunkIndex];
                const chunkStartTime = Date.now();

                console.log(`[Chunk ${chunkIndex + 1}/${chunks.length}] Processing ${chunk.length} tickers`);

                try {
                    const {updatesCount, createsCount} = await this.processPriceChunk(chunk, chunkIndex + 1, chunks.length);
                    totalUpdates += updatesCount;
                    totalCreates += createsCount;
                    processedCount += chunk.length;

                    const chunkDuration = Date.now() - chunkStartTime;
                    console.log(`[Chunk ${chunkIndex + 1}/${chunks.length}] Completed in ${chunkDuration}ms - Updates: ${updatesCount}, Creates: ${createsCount}`);
                } catch (chunkErr: any) {
                    console.error(`[Chunk ${chunkIndex + 1}/${chunks.length}] Error processing chunk:`, chunkErr.message);

                    // Fallback to individual processing for this chunk
                    console.log(`[Chunk ${chunkIndex + 1}/${chunks.length}] Falling back to individual processing...`);
                    for (const tickerPrice of chunk) {
                        try {
                            await this.setPrice(tickerPrice);
                            processedCount++;
                        } catch (individualErr: any) {
                            console.error(`Error setting individual price for ticker ${tickerPrice.ticker_internal_id}:`, individualErr.message);
                        }
                    }
                }
            }

            const duration = Date.now() - startTime;
            console.log(`Batch price setting completed in ${duration}ms for ${processedCount}/${tickerPrices.length} tickers`);

            addLogJobExecution(LogLevel.INFO, "setPrices", "Chunked batch price setting completed", {
                tickersProcessed: processedCount,
                totalTickers: tickerPrices.length,
                chunksProcessed: chunks.length,
                totalUpdates: totalUpdates,
                totalCreates: totalCreates,
                durationMs: duration,
                averageTimePerTicker: Math.round(duration / processedCount),
            });
        } catch (err: any) {
            console.error("Error in chunked batch price setting:", err.message);
            addLogJobExecution(LogLevel.ERROR, "setPrices", "Chunked batch price setting error", {
                error: err instanceof Error ? err.message : String(err),
                tickersCount: tickerPrices.length,
                processedCount: processedCount,
            });

            // Final fallback - process remaining tickers individually
            console.log("Final fallback: processing remaining tickers individually...");
            for (let i = processedCount; i < tickerPrices.length; i++) {
                try {
                    await this.setPrice(tickerPrices[i]);
                } catch (individualErr: any) {
                    console.error(`Error setting individual price for ticker ${tickerPrices[i].ticker_internal_id}:`, individualErr.message);
                }
            }
        }
    }

    /**
     * Creates chunks of ticker prices for processing
     */
    private createPriceChunks(tickerPrices: RealTimePriceEODHD[], chunkSize: number): RealTimePriceEODHD[][] {
        const chunks: RealTimePriceEODHD[][] = [];
        for (let i = 0; i < tickerPrices.length; i += chunkSize) {
            chunks.push(tickerPrices.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * Processes a single chunk of ticker prices
     */
    private async processPriceChunk(chunk: RealTimePriceEODHD[], _chunkIndex: number, _totalChunks: number): Promise<{updatesCount: number; createsCount: number}> {
        // Group prices by ticker_internal_id for efficient processing
        const priceMap = new Map<number, RealTimePriceEODHD>();
        for (const tickerPrice of chunk) {
            if (tickerPrice.ticker_internal_id) {
                priceMap.set(tickerPrice.ticker_internal_id, tickerPrice);
            }
        }

        // Get all ticker IDs for batch database operations
        const tickerIds = Array.from(priceMap.keys());

        // Batch fetch existing statistics for this chunk
        const existingStatistics = await StatisticsOfTicker.findAll({
            where: {
                ticker_internal_id: {
                    [Op.in]: tickerIds,
                },
            },
        });

        // Create a map for quick lookup
        const statisticsMap = new Map<number, StatisticsOfTicker>();
        for (const stat of existingStatistics) {
            if (stat.ticker_internal_id) {
                statisticsMap.set(stat.ticker_internal_id, stat);
            }
        }

        // Prepare updates and creates
        const updates: Promise<void>[] = [];
        const creates: any[] = [];

        for (const [tickerId, tickerPrice] of priceMap) {
            const {code, close, previousClose} = tickerPrice;

            let price = 0;
            if (close !== "NA" && typeof close === "number") {
                price = close;
            } else if (close === "NA" && previousClose !== "NA" && typeof previousClose === "number") {
                price = previousClose;
            }

            const existingStat = statisticsMap.get(tickerId);
            if (existingStat) {
                // Update existing statistic
                updates.push(existingStat.update({price}).then(() => {}));
            } else {
                // Prepare for bulk create
                creates.push({
                    price,
                    symbol_code: code,
                    ticker_internal_id: tickerId,
                });
            }
        }

        // Execute updates in smaller batches to avoid overwhelming the database
        if (updates.length > 0) {
            const updateBatchSize = 100; // Process 100 updates at a time
            for (let i = 0; i < updates.length; i += updateBatchSize) {
                const updateBatch = updates.slice(i, i + updateBatchSize);
                await Promise.all(updateBatch);
            }
        }

        // Bulk create new statistics
        if (creates.length > 0) {
            await StatisticsOfTicker.bulkCreate(creates);
        }

        return {
            updatesCount: updates.length,
            createsCount: creates.length,
        };
    }

    async setPrice(tickerPrice: RealTimePriceEODHD): Promise<void> {
        const {ticker_internal_id, code, close, previousClose} = tickerPrice;
        try {
            let price = 0;
            if (close !== "NA" && typeof close === "number") {
                price = close;
            }
            if (close === "NA" && previousClose !== "NA" && typeof previousClose === "number") {
                price = previousClose;
            }
            const statistic = await StatisticsOfTicker.findOne({
                where: {ticker_internal_id: ticker_internal_id || 0},
            });
            if (statistic) {
                statistic.price = price;
                await statistic.save();
            } else {
                const list_of_ticker = await ListOfTickers.findOne({
                    where: {id: ticker_internal_id || 0},
                });

                if (list_of_ticker) {
                    await StatisticsOfTicker.create({
                        price,
                        symbol_code: code,
                        ticker_internal_id: list_of_ticker.id || 0,
                    });
                }
            }
        } catch (err: any) {
            addLogJobExecution(LogLevel.ERROR, "setPrice", "Error when try to set price", {error: err instanceof Error ? err.message : String(err)}, ticker_internal_id || 0);
            console.log("Error when try to set Price ", err.message);
        }
    }

    async getTickers(listOfTickersId?: number[]): Promise<void> {
        const params: any = {};

        const date = new Date();
        const minutes = date.getMinutes();

        const tickers_count = await ListOfTickers.count({
            where: {
                is_enable: 1,
            },
        });

        const mod = tickers_count % 6 || 0;
        const limit = Math.trunc(tickers_count / 6) + mod;

        const page = minutes < 10 ? 0 : Math.floor(minutes / 10);

        if (listOfTickersId && listOfTickersId.length > 0) {
            params.where = {
                id: {
                    [Op.in]: listOfTickersId,
                },
                is_enable: 1,
            };
        } else {
            params.where = {
                is_enable: 1,
            };
            params.order = [["id", "ASC"]];
            params.attributes = ["primary_ticker_eodhd", "id", "symbol_code"];
            params.offset = page * limit;
            params.limit = limit;
        }

        const tickers = await ListOfTickers.findAll(params);

        this.tickers = tickers;
    }
}
