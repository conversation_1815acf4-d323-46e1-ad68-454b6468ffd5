import {DynamoDBClient} from "@aws-sdk/client-dynamodb";
import {DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand} from "@aws-sdk/lib-dynamodb";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {dynamoClient} from "../factory";
import {FundamentalDataType} from "../utils/types/EODHD/FundamentalDataType";
import {CashFlowController} from "./CashFlowController";
import {BalanceSheetController} from "./balanceSheetController";
import {IncomeStatementController} from "./incomeStatementController";
import {LogsController} from "./logsController";
import {addLogJobExecution} from "@/lib/agenda";
import {LogLevel} from "@/utils/types/logs/log";

export class DynamoController {
    client: DynamoDBClient;

    constructor() {
        this.client = dynamoClient;
    }

    async getDynamoData(key: string, table_name: string) {
        try {
            const docClient = DynamoDBDocumentClient.from(this.client);

            const command = new GetCommand({
                TableName: table_name,
                Key: {
                    ticker_internal_id: String(key),
                },
            });

            const response = await docClient.send(command);

            return response;
        } catch (error: any) {
            console.log("error when try to get dynamo", error.message);
        }
    }

    async saveDynamoData(key: string, table_name: string, body: any) {
        try {
            const docClient = DynamoDBDocumentClient.from(this.client);

            console.log(key, body);

            const command = new PutCommand({
                TableName: table_name,
                Item: {
                    ticker_internal_id: String(key),
                    body: JSON.stringify(body),
                },
            });

            const response = await docClient.send(command);

            return response;
        } catch (error: any) {
            console.log("error when try to save dynamo", error.message);
        }
    }
    async updateDynamoData(key: string, table_name: string, body: any) {
        try {
            const docClient = DynamoDBDocumentClient.from(this.client);

            const command = new UpdateCommand({
                TableName: table_name,
                Key: {
                    ticker_internal_id: String(key),
                },
                UpdateExpression: "set body = :body",
                ExpressionAttributeValues: {
                    ":body": JSON.stringify(body),
                },
                ReturnValues: "ALL_NEW",
            });

            const response = await docClient.send(command);

            return response;
        } catch (error: any) {
            console.log("error when try to update dynamo", error.message);
        }
    }
}
