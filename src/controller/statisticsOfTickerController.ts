import {ListOfTickers} from "@/entities/consolidated_data/ListOfTickers";
import {iMessageSQS} from "../repositories/iMessageSQS";
import {StatisticsOfTickerRepository} from "../repositories/implements/StatisticsOfTickerRepository";

export class StatisticsOfTickerController {
    statisticsRepository: StatisticsOfTickerRepository;

    constructor() {
        this.statisticsRepository = new StatisticsOfTickerRepository();
    }

    async getTickers(TickerInternalId?: number[]) {
        await this.statisticsRepository.getTickers(TickerInternalId);
    }

    async getTickersStatisticsAndCalculate(tickers: ListOfTickers[]) {
        await this.statisticsRepository.getTickersStatisticsAndCalculate(tickers);
    }
}
