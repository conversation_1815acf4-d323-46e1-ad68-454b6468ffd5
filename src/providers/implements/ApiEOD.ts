import {AxiosResponse} from "axios";
import {IApiProvider} from "../iApiProvider";
import {apiEOD} from "../../services/apiEOD";
import {RealTimePriceEODHD} from "../../repositories/implements/RealTimePriceDAO";
import {iDividendsEODHD} from "../../repositories/iDividendsEODHD";
import {iSplitsDAO} from "../../repositories/iSplitsDAO";

export class ApiEOD implements IApiProvider {
    async find(stock: string, params?: string): Promise<any> {
        const {data}: AxiosResponse = await apiEOD.getFundamentaData(stock, params);

        return data;
    }

    async findDividends(stock: string): Promise<iDividendsEODHD[]> {
        const {data}: AxiosResponse = await apiEOD.getDividends(stock);

        return data;
    }

    async findSplits(stock: string): Promise<iSplitsDAO[]> {
        const response = await apiEOD.getSplits(stock);

        if (response?.status && response?.status === 200) return response.data;

        return [];
    }

    async findPrice(stock: string, others?: string[]): Promise<RealTimePriceEODHD[] | RealTimePriceEODHD> {
        const {data}: AxiosResponse = await apiEOD.getPrice(stock, others);

        return data;
    }
}
